<?php

namespace app\applent\logic;

use app\common\model\gift\Gift;
use app\common\model\gift\GiftRecord;
use app\common\model\user\User;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
use app\common\service\ConfigService;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 礼物业务逻辑类
 */
class GiftLogic
{
    /**
     * @notes 赠送礼物
     * @param int $userId 赠送用户ID
     * @param array $params 参数
     * @return array
     */
    public static function sendGift($userId, $params)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 1. 防重复提交检查
            $cacheKey = "gift_send_{$userId}_{$params['to_user_id']}_{$params['gift_id']}";
            if (Cache::get($cacheKey)) {
                throw new \Exception('操作过于频繁，请稍后再试');
            }
            Cache::set($cacheKey, 1, 60); // 1分钟防重复

            // 2. 基础验证
            if ($userId == $params['to_user_id']) {
                throw new \Exception('不能给自己赠送礼物');
            }

            // 3. 获取礼物信息
            $gift = Gift::find($params['gift_id']);
            if (!$gift) {
                throw new \Exception('礼物不存在');
            }

            // 4. 获取接收用户信息
            $toUser = User::find($params['to_user_id']);
            if (!$toUser) {
                throw new \Exception('接收用户不存在');
            }

            // 5. 计算总金额
            $totalAmount = $gift->price * $params['gift_count'];

            // 6. 检查用户余额
            $userBalance = UserBalance::getUserBalance($userId);
            if (!$userBalance || $userBalance['balance'] < $totalAmount) {
                throw new \Exception('余额不足，请先充值');
            }

            // 7. 生成订单号
            $orderNo = 'GIFT_' . date('YmdHis') . mt_rand(1000, 9999);

            // 8. 扣除用户余额
            self::deductUserBalance($userId, $totalAmount, $orderNo, $gift, $params);

            // 9. 计算并增加接收用户收益
            $actualIncome = self::addUserIncome($params['to_user_id'], $totalAmount, $orderNo, $gift, $params, $userId);

            // 10. 处理返佣
            self::handleRebateCommission($params['to_user_id'], $totalAmount, $orderNo, $userId);

            // 11. 创建礼物记录
            $giftRecord = self::createGiftRecord($userId, $params, $gift, $totalAmount, $actualIncome, $orderNo);

            // 12. 全局广播（如果需要）
            self::sendGlobalGiftMessage($giftRecord, $gift, $params);

            // 提交事务
            Db::commit();

            return [
                'code' => 1,
                'msg' => '礼物赠送成功',
                'data' => [
                    'order_no' => $orderNo,
                    'gift_name' => $gift->name,
                    'gift_count' => $params['gift_count'],
                    'total_amount' => $totalAmount,
                    'remaining_balance' => $userBalance['balance'] - $totalAmount,
                    'is_global' => $gift->is_global ?? 0
                ]
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            return [
                'code' => 0,
                'msg' => '礼物赠送失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * @notes 扣除用户余额
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @param string $orderNo 订单号
     * @param object $gift 礼物对象
     * @param array $params 参数
     */
    private static function deductUserBalance($userId, $amount, $orderNo, $gift, $params)
    {
        $userBalance = UserBalance::getUserBalance($userId);
        
        $beforeBalance = $userBalance['balance'];
        $afterBalance = $beforeBalance - $amount;
        
        // 更新余额
        UserBalance::where('user_id', $userId)
            ->update([
                'balance' => $afterBalance,
                'update_time' => time()
            ]);
        
        // 记录余额变动日志
        UserCoinLog::addGiftSendLog(
            $userId,
            $beforeBalance,
            $afterBalance,
            $amount,
            $orderNo,
            $params['to_user_id'],
            $gift->name,
            $params['gift_count']
        );
    }

    /**
     * @notes 增加用户收益
     * @param int $toUserId 接收用户ID
     * @param float $totalAmount 总金额
     * @param string $orderNo 订单号
     * @param object $gift 礼物对象
     * @param array $params 参数
     * @param int $fromUserId 赠送用户ID
     * @return float 实际收益
     */
    private static function addUserIncome($toUserId, $totalAmount, $orderNo, $gift, $params, $fromUserId)
    {
        // 计算抽成
        $commissionRate = self::calculateCommission($toUserId);
        $actualIncome = $totalAmount * (1 - $commissionRate / 100);
        
        // 获取用户余额信息
        $userBalance = UserBalance::getUserBalance($toUserId);
        if (!$userBalance) {
            $userBalance = UserBalance::createUserBalance($toUserId);
        }
        
        $beforeIncome = $userBalance['income'];
        $afterIncome = $beforeIncome + $actualIncome;
        
        // 更新收益
        UserBalance::where('user_id', $toUserId)
            ->update([
                'income' => $afterIncome,
                'update_time' => time()
            ]);
        
        // 记录收益变动日志
        UserIncomeLog::addGiftReceiveLog(
            $toUserId,
            $beforeIncome,
            $afterIncome,
            $actualIncome,
            $orderNo,
            $fromUserId,
            $gift->name,
            $params['gift_count'],
            $commissionRate,
            $totalAmount
        );
        
        return $actualIncome;
    }

    /**
     * @notes 计算抽成比例
     * @param int $userId 用户ID
     * @return float 抽成比例
     */
    private static function calculateCommission($userId)
    {
        $user = User::find($userId);
        if (!$user) {
            return 0;
        }

        // 检查用户是否开启个人抽成设置
        if ($user->is_open_gift_commission == 1) {
            // 从用户返佣比例表获取
            $userRebate = \app\common\model\user\UserRebateRatio::where('user_id', $userId)->find();
            if ($userRebate && isset($userRebate->gift_commission)) {
                return floatval($userRebate->gift_commission);
            }
        }

        // 使用系统默认配置
        return floatval(ConfigService::get('systemconfig', 'gift_commission', 0));
    }

    /**
     * @notes 处理返佣
     * @param int $toUserId 接收礼物用户ID
     * @param float $totalAmount 礼物总金额
     * @param string $orderNo 订单号
     * @param int $fromUserId 赠送用户ID
     */
    private static function handleRebateCommission($toUserId, $totalAmount, $orderNo, $fromUserId)
    {
        try {
            // 使用公共函数计算返佣
            $commissionInfo = calculate_user_commission($toUserId, $totalAmount, 2); // 类型2=礼物
            
            // 处理一级返佣
            if (isset($commissionInfo['level1'])) {
                self::processRebate($commissionInfo['level1'], $orderNo, $fromUserId, 1);
            }

            // 处理二级返佣
            if (isset($commissionInfo['level2'])) {
                self::processRebate($commissionInfo['level2'], $orderNo, $fromUserId, 2);
            }
        } catch (\Exception $e) {
            // 返佣失败不影响主流程，只记录日志
            Log::error('礼物返佣处理失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 处理单级返佣
     * @param array $rebateData 返佣数据
     * @param string $orderNo 订单号
     * @param int $fromUserId 赠送用户ID
     * @param int $level 返佣级别
     */
    private static function processRebate($rebateData, $orderNo, $fromUserId, $level)
    {
        // 增加返佣用户收益
        $userBalance = UserBalance::getUserBalance($rebateData['user_id']);
        if (!$userBalance) {
            $userBalance = UserBalance::createUserBalance($rebateData['user_id']);
        }
        
        $beforeIncome = $userBalance['income'];
        $afterIncome = $beforeIncome + $rebateData['commission'];
        
        UserBalance::where('user_id', $rebateData['user_id'])
            ->update([
                'income' => $afterIncome,
                'update_time' => time()
            ]);
        
        // 记录返佣日志
        UserIncomeLog::addGiftCommissionLog(
            $rebateData['user_id'],
            $beforeIncome,
            $afterIncome,
            $rebateData['commission'],
            $level,
            $orderNo,
            $fromUserId,
            $rebateData['rate'],
            0 // 原始金额在返佣中不需要记录
        );
    }

    /**
     * @notes 创建礼物记录
     */
    private static function createGiftRecord($userId, $params, $gift, $totalAmount, $actualIncome, $orderNo)
    {
        return GiftRecord::create([
            'user_id' => $userId,
            'to_user_id' => $params['to_user_id'],
            'gift_id' => $params['gift_id'],
            'gift_name' => $gift->name,
            'gift_count' => $params['gift_count'],
            'gift_price' => $gift->price,
            'total_amount' => $totalAmount,
            'actual_income' => $actualIncome,
            'commission_rate' => self::calculateCommission($params['to_user_id']),
            'source_type' => $params['source_type'],
            'source_id' => $params['source_id'] ?? '',
            'order_no' => $orderNo,
            'is_global' => $gift->is_global ?? 0,
            'create_time' => time()
        ]);
    }

    /**
     * @notes 发送全局礼物消息
     */
    private static function sendGlobalGiftMessage($giftRecord, $gift, $params)
    {
        try {
            if ($gift->is_global == 1) {
                // 调用IM全局广播
                $messageData = [
                    'type' => 777, // 全局礼物消息类型
                    'gift_id' => $gift->id,
                    'gift_name' => $gift->name,
                    'gift_count' => $params['gift_count'],
                    'from_user_id' => $giftRecord->user_id,
                    'to_user_id' => $giftRecord->to_user_id,
                    'total_amount' => $giftRecord->total_amount
                ];
                
                // 这里调用IM广播接口
                // ImService::sendGlobalMessage($messageData);
            }
        } catch (\Exception $e) {
            // 广播失败不影响主流程
            Log::error('全局礼物广播失败：' . $e->getMessage());
        }
    }
}
